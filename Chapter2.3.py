
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from time import sleep
import pandas as pd
import re

# 1. Configure Edge options
options = Options()
options.add_argument("--start-maximized")  # Open Edge in maximized mode
options.add_experimental_option(name= "detach", value= True) # for make webdriver not close browser after do code finish
#----------1. Configure Edge options-------End

driver = webdriver.Edge(options=options) #2. Start Edge WebDriver
driver.get("https://twitter.com/Cdiscount") #3. Open the web page
sleep(4.0)

#driver.execute_script("alert('This is an alret')") # execute some javascript in web page.
#sleep(2.0)

driver.execute_script("window.scrollBy(0, 500);")  # Scroll down 500px
sleep(2.0)
driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")


"""
#Username = driver.find_element(By.ID, "username")
Username = driver.find_element(By.XPATH, "//*[@id='username']") 
Username.click()
Username.send_keys("<EMAIL>")
Password = driver.find_element(By.ID, "password")
Password.click()
Password.send_keys("tingy_ee337")
Login = driver.find_element(By.XPATH, "//*[@id='organic-div']/form/div[4]/button")
Login.click()
sleep(3.0)
"""